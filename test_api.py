"""
Test script for the multi-tenant FastAPI application
Tests login and chat endpoints
"""

import requests
import json

# API base URL
BASE_URL = "http://localhost:8000"

def test_login():
    """Test the login endpoint"""
    print("🔐 Testing login endpoint...")

    # OAuth2 form data format
    login_data = {
        "grant_type": "password",
        "username": "admin",
        "password": "admin123",
        "client_id": "ambition-guru",
        "scope": ""
    }

    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        print(f"   Request URL: {BASE_URL}/api/v1/login")
        print(f"   Request data: {login_data}")
        print(f"   Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"   Token: {result['access_token'][:50]}...")
            print(f"   User: {result['username']} ({result['role']})")
            print(f"   Tenant: {result['tenant_label']}")
            return result['access_token']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Login error: {e}")
        return None


def test_chat(token):
    """Test the chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    
    if not token:
        print("❌ No token available for chat test")
        return
    
    chat_data = {
        "message": "Hello! What courses do you offer?"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json=chat_data,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat successful!")
            print(f"   User message: {chat_data['message']}")
            print(f"   Bot response: {result['response'][:100]}...")
            print(f"   Thread ID: {result['thread_id']}")
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Chat error: {e}")


def test_verify_token(token):
    """Test the verify token endpoint"""
    print("\n🔍 Testing verify token endpoint...")
    
    if not token:
        print("❌ No token available for verification test")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/verify_token",
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Token verification successful!")
            print(f"   Valid: {result['valid']}")
            print(f"   User: {result['username']} ({result['role']})")
            print(f"   Tenant ID: {result['tenant_id']}")
        else:
            print(f"❌ Token verification failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Token verification error: {e}")


def test_health_check():
    """Test the health check endpoint"""
    print("\n🏥 Testing health check endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check successful!")
            print(f"   Status: {result['status']}")
            print(f"   Service: {result['service']}")
            print(f"   Version: {result['version']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")


def test_multiple_users():
    """Test login with different users"""
    print("\n👥 Testing multiple user logins...")

    users = [
        {"username": "admin", "password": "admin123", "role": "admin"},
        {"username": "supervisor", "password": "supervisor123", "role": "supervisor"},
        {"username": "agent1", "password": "agent123", "role": "agent"}
    ]

    tokens = {}

    for user in users:
        print(f"\n   Testing {user['username']} ({user['role']})...")

        login_data = {
            "grant_type": "password",
            "username": user["username"],
            "password": user["password"],
            "client_id": "ambition-guru",
            "scope": ""
        }

        try:
            response = requests.post(
                f"{BASE_URL}/api/v1/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )

            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {user['username']} login successful!")
                tokens[user['username']] = result['access_token']
            else:
                print(f"   ❌ {user['username']} login failed: {response.status_code}")
                print(f"      Error: {response.text}")

        except Exception as e:
            print(f"   ❌ {user['username']} login error: {e}")

    return tokens


def test_chat_conversation(token):
    """Test a conversation with multiple messages"""
    print("\n💬 Testing chat conversation...")

    if not token:
        print("❌ No token available for chat conversation test")
        return

    messages = [
        "Hello! What courses do you offer?",
        "I'm interested in IELTS preparation",
        "What are the timings for IELTS classes?",
        "Can I book a demo class?"
    ]

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    for i, message in enumerate(messages, 1):
        print(f"\n   Message {i}: {message}")

        chat_data = {"message": message}

        try:
            response = requests.post(
                f"{BASE_URL}/api/v1/chat",
                json=chat_data,
                headers=headers
            )

            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Response: {result['response'][:80]}...")
                print(f"   Thread ID: {result['thread_id']}")
            else:
                print(f"   ❌ Chat failed: {response.status_code}")
                print(f"      Error: {response.text}")
                break

        except Exception as e:
            print(f"   ❌ Chat error: {e}")
            break


if __name__ == "__main__":
    print("🚀 Starting comprehensive API tests...")
    print("="*60)

    # Test health check first
    test_health_check()

    # Test single user login
    print("\n" + "="*60)
    token = test_login()

    # Test token verification
    test_verify_token(token)

    # Test basic chat
    test_chat(token)

    # Test multiple users
    print("\n" + "="*60)
    tokens = test_multiple_users()

    # Test chat conversation with admin user
    if tokens.get('admin'):
        print("\n" + "="*60)
        test_chat_conversation(tokens['admin'])

    print("\n" + "="*60)
    print("🎯 Comprehensive API tests completed!")

    if token:
        print("\n📝 Manual testing information:")
        print(f"   API Documentation: {BASE_URL}/docs")
        print(f"   Base URL: {BASE_URL}")
        print("\n� Available test accounts:")
        print("   • admin / admin123 (role: admin)")
        print("   • supervisor / supervisor123 (role: supervisor)")
        print("   • agent1 / agent123 (role: agent)")
        print("\n🔑 Login format:")
        print("   POST /api/v1/login")
        print("   Content-Type: application/x-www-form-urlencoded")
        print("   Data: grant_type=password&username=admin&password=admin123&client_id=ambition-guru")
        print("\n💬 Chat format:")
        print("   POST /api/v1/chat")
        print("   Authorization: Bearer <token>")
        print("   Content-Type: application/json")
        print('   Data: {"message": "Hello!"}')
    else:
        print("\n❌ Login failed - check your setup and try again")
