#!/usr/bin/env python3
"""
Comprehensive Chat Testing Script
Tests all functionality including search info, course verification, memory management
"""

import requests
import json
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8001"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInVzZXJfaWQiOiI2ODZhMDIxNzllOGVlZTBkOWYyYzVkYjgiLCJyb2xlIjoiYWRtaW4iLCJ0ZW5hbnRfaWQiOiI2ODZhMDIxNzllOGVlZTBkOWYyYzVkYjYiLCJleHAiOjE3NTE3OTk0MjF9.Z9jxsraEBqUKnDDpeTfVGzhaECMgNOWaR0hN-dwh43c"

def send_chat_message(message: str) -> Dict[str, Any]:
    """Send a chat message and return the response"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TOKEN}"
    }
    
    data = {"message": message}
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/chat", headers=headers, json=data, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"❌ Error sending message: {e}")
        return {"error": str(e)}

def print_response(test_name: str, message: str, response: Dict[str, Any]):
    """Print formatted test response"""
    print(f"\n{'='*60}")
    print(f"🧪 TEST: {test_name}")
    print(f"📤 USER: {message}")
    print(f"📥 AGENT: {response.get('response', 'ERROR: ' + str(response))}")
    print(f"🆔 Thread ID: {response.get('thread_id', 'N/A')}")
    print(f"{'='*60}")

def test_search_information():
    """Test 1: Search Information Queries"""
    print("\n🔍 TESTING SEARCH INFORMATION FUNCTIONALITY")
    
    info_queries = [
        "What support do you provide?",
        "How can I contact customer service?", 
        "What are your payment options?",
        "Do you have mobile app?",
        "What certificates do you provide?",
        "Are classes available online?",
        "What languages do you support?"
    ]
    
    for i, query in enumerate(info_queries, 1):
        response = send_chat_message(query)
        print_response(f"Info Query {i}", query, response)
        time.sleep(2)  # Avoid overwhelming the server

def test_wrong_course_booking():
    """Test 2: Wrong Course Name Booking - Detection and Suggestions"""
    print("\n❌ TESTING WRONG COURSE NAME DETECTION")
    
    wrong_course_queries = [
        "I want to book Python course",  # Should suggest alternatives
        "Do you have Java programming?",  # Should say no and suggest alternatives
        "I need Machine Learning course",  # Should suggest alternatives
        "Book me React Native course",  # Should suggest alternatives
        "I want to enroll in PHP course"  # Should suggest alternatives
    ]
    
    for i, query in enumerate(wrong_course_queries, 1):
        response = send_chat_message(query)
        print_response(f"Wrong Course {i}", query, response)
        time.sleep(2)

def test_correct_course_booking():
    """Test 3: Correct Course Booking"""
    print("\n✅ TESTING CORRECT COURSE BOOKING")
    
    # Test with real courses from our catalog
    correct_course_queries = [
        "I want to book SEE Bridge Course",
        "Do you have IELTS preparation?",
        "I need Korean language course",
        "Book me German A1 level",
        "I want CSIT entrance prep"
    ]
    
    for i, query in enumerate(correct_course_queries, 1):
        response = send_chat_message(query)
        print_response(f"Correct Course {i}", query, response)
        time.sleep(2)

def test_booking_flow_with_user_info():
    """Test 4: Complete Booking Flow with User Information"""
    print("\n👤 TESTING COMPLETE BOOKING FLOW")
    
    booking_flow = [
        "I want to book IELTS Preparation course",
        "My name is Ram Sharma",
        "My phone number is 9841234567", 
        "My <NAME_EMAIL>",
        "Yes, confirm my booking"
    ]
    
    for i, message in enumerate(booking_flow, 1):
        response = send_chat_message(message)
        print_response(f"Booking Step {i}", message, response)
        time.sleep(3)  # Give more time for booking processing

def test_multiple_course_interest():
    """Test 5: Multiple Course Selections and Memory"""
    print("\n📚 TESTING MULTIPLE COURSE SELECTIONS")
    
    multiple_course_flow = [
        "What Korean language courses do you have?",
        "I'm interested in Korean Level 1",
        "Actually, I also want to know about German courses",
        "I want both Korean Level 1 and German A1",
        "Change my selection to only German B1 level",
        "What courses am I currently interested in?"
    ]
    
    for i, message in enumerate(multiple_course_flow, 1):
        response = send_chat_message(message)
        print_response(f"Multiple Courses {i}", message, response)
        time.sleep(2)

def test_course_name_changes():
    """Test 6: Course Name Changes and Updates"""
    print("\n🔄 TESTING COURSE NAME CHANGES")
    
    course_change_flow = [
        "I want to book BBS course",
        "Actually, change that to BBA course",
        "No wait, I prefer CSIT entrance preparation",
        "What course am I currently booking?"
    ]
    
    for i, message in enumerate(course_change_flow, 1):
        response = send_chat_message(message)
        print_response(f"Course Change {i}", message, response)
        time.sleep(2)

def test_memory_persistence():
    """Test 7: Memory Persistence Across Conversations"""
    print("\n🧠 TESTING MEMORY PERSISTENCE")
    
    memory_test_flow = [
        "Hello, I'm back",
        "What information do you have about me?",
        "What courses was I interested in?",
        "Add NASU Level 1 to my interests",
        "Show me all my course selections"
    ]
    
    for i, message in enumerate(memory_test_flow, 1):
        response = send_chat_message(message)
        print_response(f"Memory Test {i}", message, response)
        time.sleep(2)

def test_edge_cases():
    """Test 8: Edge Cases and Error Handling"""
    print("\n⚠️ TESTING EDGE CASES")
    
    edge_cases = [
        "",  # Empty message
        "Book course",  # Incomplete request
        "I want to book 5 courses at once",  # Multiple booking
        "Cancel my booking",  # Cancellation
        "What's the price of IELTS course?",  # Price inquiry
        "When do classes start?"  # Schedule inquiry
    ]
    
    for i, message in enumerate(edge_cases, 1):
        if message:  # Skip empty message for now
            response = send_chat_message(message)
            print_response(f"Edge Case {i}", message, response)
            time.sleep(2)

def main():
    """Run all comprehensive tests"""
    print("🚀 STARTING COMPREHENSIVE CHAT TESTING")
    print(f"🎯 Target: {BASE_URL}")
    print(f"🔑 Using token: {TOKEN[:20]}...")
    
    # Check if server is running
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server health check failed")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server. Make sure it's running on port 8001")
        return
    
    # Run all test suites
    test_functions = [
        test_search_information,
        test_wrong_course_booking, 
        test_correct_course_booking,
        test_booking_flow_with_user_info,
        test_multiple_course_interest,
        test_course_name_changes,
        test_memory_persistence,
        test_edge_cases
    ]
    
    for test_func in test_functions:
        try:
            test_func()
            print(f"\n⏳ Waiting 5 seconds before next test suite...")
            time.sleep(5)
        except KeyboardInterrupt:
            print("\n🛑 Testing interrupted by user")
            break
        except Exception as e:
            print(f"\n❌ Error in {test_func.__name__}: {e}")
            continue
    
    print("\n🎉 COMPREHENSIVE TESTING COMPLETED!")
    print("📊 Review the results above to verify all functionality is working correctly.")

if __name__ == "__main__":
    main()
