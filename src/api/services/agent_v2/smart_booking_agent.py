"""
Smart Booking Agent - Single intelligent tool following LangGraph customer support pattern
Handles entire booking workflow with context understanding
"""

import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import MessagesState, StateGraph, START
import os
from dotenv import load_dotenv
from .user_memory import get_user_memory_manager

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Global booking sessions storage (in production, use proper database)
_booking_sessions = {}


class SmartBookingAgent:
    """
    Smart Booking Agent - Single tool that handles entire booking workflow
    Uses LangGraph memory patterns and context understanding
    """
    
    def __init__(self):
        """Initialize the smart booking agent"""
        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Memory for conversation context
        self.memory = MemorySaver()

        # User memory manager for persistent storage
        self.user_memory = get_user_memory_manager()
        
        # Dynamic booking requirements
        self.requirements = {
            "name": {"required": True, "type": "string", "prompt": "your full name"},
            "course_product": {"required": True, "type": "string", "prompt": "the course/product you're interested in"},
            "email": {"required": True, "type": "email", "prompt": "your email address"},
            "phone": {"required": True, "type": "phone", "prompt": "your phone number"}
        }
        
        # Mock available slots
        self.available_slots = self._generate_mock_slots()
        self.booked_appointments = []
        
        logger.info("✅ Smart Booking Agent initialized")
    
    def handle_booking_request(self, user_message: str, thread_id: str = "default") -> str:
        """
        Main entry point - intelligently handles any booking-related message
        
        Args:
            user_message: User's message about booking
            thread_id: Thread ID for session management
            
        Returns:
            Appropriate response based on context and intent
        """
        logger.info(f"📅 Processing booking request: '{user_message}' for thread: {thread_id}")
        
        # Get or create booking session
        session = self._get_or_create_session(thread_id)
        
        # Analyze user intent and extract information
        intent_analysis = self._analyze_booking_intent(user_message, session, thread_id)
        
        # Handle based on intent
        if intent_analysis["action"] == "start_booking":
            return self._start_booking_process(session, thread_id)
        
        elif intent_analysis["action"] == "provide_info":
            return self._collect_information(intent_analysis["extracted_info"], session, thread_id)
        
        elif intent_analysis["action"] == "select_slot":
            return self._handle_slot_selection(intent_analysis["slot_number"], session, thread_id)
        
        elif intent_analysis["action"] == "update_info":
            return self._update_information(intent_analysis["field"], intent_analysis["value"], session, thread_id)
        
        elif intent_analysis["action"] == "show_status":
            return self._show_booking_status(session, thread_id)
        
        elif intent_analysis["action"] == "confirm_booking":
            return self._confirm_booking(session, thread_id)
        
        else:
            return self._handle_unclear_intent(user_message, session, thread_id)
    
    def _analyze_booking_intent(self, user_message: str, session: Dict, thread_id: str) -> Dict[str, Any]:
        """
        Analyze user message to understand booking intent - NO HARDCODING
        Uses LLM to understand context and extract multiple pieces of information
        """
        # Create context from session
        session_context = self._format_session_context(session)

        # System prompt for intelligent intent analysis
        system_prompt = f"""You are an intelligent booking assistant. Analyze the user's message and extract ALL relevant information.

Current booking session:
{session_context}

IMPORTANT RULES:
1. If user provides information we already have, UPDATE it (don't ask again)
2. Extract ALL information from the message (name, course, email, phone)
3. If user provides multiple pieces of info, extract them all
4. Be smart about context - understand natural language
5. Ask informaton in order for example Please provide your name, course/product, email, and phone number.

Required booking information:
- name: Full name
- course_product: Course or product they want
- email: Email address
- phone: Phone number

Analyze the message and determine:
1. What action they want (start_booking, provide_info, select_slot, show_status, etc.)
2. Extract ALL information provided (even if we already have it - we'll update)
3. Understand slot selection (numbers, "first", "second", etc.)

Examples:
- "I want to book IELTS course, my name is John" → extract course_product="IELTS course", name="John"
- "<EMAIL>" → extract email="<EMAIL>"
- "Update my <NAME_EMAIL>" → update email
- "I'll take slot 2" or "first option" → select slot

Respond in JSON format:
{{
    "action": "provide_info|select_slot|start_booking|show_status|unclear",
    "extracted_info": {{"field": "value", "field2": "value2"}} or null,
    "slot_number": "number" or null,
    "reasoning": "what you understood"
}}"""

        try:
            # Get intent analysis from LLM
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User message: {user_message}")
            ])

            # Clean and parse JSON response
            content = response.content.strip()

            # Try to extract JSON from response
            import json
            try:
                content=json.loads(content)
            except ValueError:
                logger.warning(f"JSON not found in response: {content}")
                if content.startswith("```json"):
                    content = content.replace("```json", "").replace("```", "").strip()
                elif content.startswith("```"):
                    content = content.replace("```", "").strip()
                analysis = json.loads(content)

            logger.info(f"🎯 Intent analysis: {analysis['action']} - {analysis['reasoning']}")

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing intent: {e} | Response: {response.content[:100] if 'response' in locals() else 'No response'}")
            # Fallback to simple pattern matching
            return self._simple_intent_analysis(user_message, session)
    
    def _simple_intent_analysis(self, user_message: str, session: Dict) -> Dict[str, Any]:
        """Fallback simple intent analysis - extract multiple info pieces"""
        message_lower = user_message.lower()
        extracted_info = {}

        # Check for booking start keywords
        if any(word in message_lower for word in ["book", "appointment", "schedule", "reserve"]):
            # Try to extract course/product from booking request
            course_keywords = ["course", "class", "program", "training", "ielts", "german", "python", "english"]
            for keyword in course_keywords:
                if keyword in message_lower:
                    # Extract course context
                    words = user_message.split()
                    for i, word in enumerate(words):
                        if keyword.lower() in word.lower():
                            # Take surrounding context
                            start = max(0, i-2)
                            end = min(len(words), i+3)
                            course_text = " ".join(words[start:end])
                            extracted_info["course_product"] = course_text.strip()
                            break
                    break

        # Check for slot selection
        slot_match = re.search(r'(?:slot\s*)?(\d+)|(?:first|1st)|(?:second|2nd)|(?:third|3rd)', message_lower)
        if slot_match and session.get("status") == "selecting_slot":
            slot_num = "1" if "first" in message_lower or "1st" in message_lower else \
                      "2" if "second" in message_lower or "2nd" in message_lower else \
                      "3" if "third" in message_lower or "3rd" in message_lower else \
                      slot_match.group(1) if slot_match.group(1) else "1"
            return {"action": "select_slot", "extracted_info": None, "slot_number": slot_num, "reasoning": "Slot selection detected"}

        # Extract email
        email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', user_message)
        if email_match:
            extracted_info["email"] = email_match.group()

        # Extract phone
        phone_match = re.search(r'[\+]?[\d\-\(\)\s]{10,}', user_message)
        if phone_match:
            extracted_info["phone"] = phone_match.group().strip()

        # Extract name patterns
        name_patterns = [
            r'(?:my name is|i am|i\'m|name:)\s+([A-Za-z\s]+)',
            r'^([A-Za-z]+\s+[A-Za-z]+)$',  # Simple "First Last" pattern
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, user_message, re.IGNORECASE)
            if name_match:
                potential_name = name_match.group(1).strip()
                # Validate it looks like a name (not email, phone, etc.)
                if not re.search(r'[@\d]', potential_name) and len(potential_name.split()) >= 2:
                    extracted_info["name"] = potential_name
                    break

        # If no specific patterns matched, try to infer based on missing fields
        if not extracted_info:
            missing_fields = self._get_missing_fields(session)
            if missing_fields:
                # Smart inference based on content
                if not re.search(r'[@\d\-\+\(\)]', user_message) and len(user_message.split()) <= 4:
                    # Likely a name or course
                    if "name" in missing_fields:
                        extracted_info["name"] = user_message.strip()
                    elif "course_product" in missing_fields:
                        extracted_info["course_product"] = user_message.strip()

        if extracted_info:
            return {"action": "provide_info", "extracted_info": extracted_info, "slot_number": None, "reasoning": f"Extracted: {list(extracted_info.keys())}"}

        return {"action": "unclear", "extracted_info": None, "slot_number": None, "reasoning": "Could not determine intent"}
    
    def _get_or_create_session(self, thread_id: str) -> Dict:
        """Get existing session or create new one"""
        if thread_id not in _booking_sessions:
            _booking_sessions[thread_id] = {
                "status": "new",
                "collected_data": {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        return _booking_sessions[thread_id]
    
    def _format_session_context(self, session: Dict) -> str:
        """Format session context for LLM"""
        if session["status"] == "new":
            return "No active booking session. User needs to start booking process."
        
        context = f"Booking Status: {session['status']}\n"
        context += f"Collected Information:\n"
        
        for field, value in session["collected_data"].items():
            field_prompt = self.requirements[field]["prompt"]
            context += f"  • {field_prompt.title()}: {value}\n"
        
        missing = self._get_missing_fields(session)
        if missing:
            context += f"Still needed: {', '.join([self.requirements[f]['prompt'] for f in missing])}\n"
        
        return context

    def _save_user_info_to_memory(self, session: Dict, thread_id: str) -> None:
        """Save collected user information to persistent memory"""
        try:
            collected_data = session.get("collected_data", {})

            # Save user info
            user_info_updates = {}
            if "name" in collected_data:
                user_info_updates["name"] = collected_data["name"]
            if "email" in collected_data:
                user_info_updates["email"] = collected_data["email"]
            if "phone" in collected_data:
                user_info_updates["phone"] = collected_data["phone"]

            if user_info_updates:
                self.user_memory.update_user_info(thread_id, **user_info_updates)
                logger.info(f"💾 Saved user info to memory: {list(user_info_updates.keys())}")

            # Save course selection if available
            if "course_product" in collected_data:
                course_name = collected_data["course_product"]
                # Try to extract course code (this could be enhanced with course verification)
                course_code = self._extract_course_code(course_name)
                self.user_memory.add_course_selection(thread_id, course_name, course_code)
                logger.info(f"📚 Saved course selection: {course_name}")

        except Exception as e:
            logger.error(f"Error saving user info to memory: {e}")

    def _extract_course_code(self, course_name: str) -> str:
        """Extract or generate course code from course name"""
        # Simple mapping for known courses
        course_mappings = {
            "see bridge": "SEE-BRIDGE",
            "bbs": "BBS-SEM1",
            "bba": "BBA-SEM1",
            "csit": "CSIT-ENT",
            "ielts": "IELTS-PREP",
            "korean": "KOR-L1",
            "german": "GER-A1",
            "eis": "EIS-G11",
            "wwis": "WWIS-G12",
            "nasu": "NASU-L1",
            "nimabi": "NIMABI-PREP"
        }

        course_lower = course_name.lower()
        for key, code in course_mappings.items():
            if key in course_lower:
                return code

        # Generate a code if no mapping found
        return course_name.upper().replace(" ", "-")[:10]

    def _start_booking_process(self, session: Dict, thread_id: str) -> str:
        """Start the booking process"""
        session["status"] = "collecting_info"
        session["updated_at"] = datetime.now().isoformat()
        
        return self._get_next_step(session)
    
    def _collect_information(self, extracted_info: Dict, session: Dict, thread_id: str) -> str:
        """Collect and validate information - smart about updates"""
        if not extracted_info:
            return "I didn't understand what information you provided. Could you please clarify?"

        updates_made = []
        errors = []

        for field, value in extracted_info.items():
            # Validate the field
            validation = self._validate_field(field, value)
            if not validation["valid"]:
                errors.append(f"❌ {validation['message']}")
                continue

            # Check if this is an update or new info
            old_value = session["collected_data"].get(field)
            session["collected_data"][field] = validation["value"]
            session["updated_at"] = datetime.now().isoformat()

            if old_value and old_value != validation["value"]:
                updates_made.append(f"Updated {self.requirements[field]['prompt']}: {old_value} → {validation['value']}")
                logger.info(f"🔄 Updated {field}: {old_value} → {value} for thread: {thread_id}")
            else:
                logger.info(f"📝 Collected {field}: {value} for thread: {thread_id}")

        # Save collected information to persistent memory
        self._save_user_info_to_memory(session, thread_id)

        # Handle errors
        if errors:
            return "\n".join(errors)

        # Show updates if any
        response = ""
        if updates_made:
            response = "✅ " + "\n".join(updates_made) + "\n\n"

        # Get next step
        return response + self._get_next_step(session)
    
    def _get_next_step(self, session: Dict) -> str:
        """Determine next step - smart about not re-asking"""
        missing_fields = self._get_missing_fields(session)

        if missing_fields:
            # Still collecting information - but be smart about it
            session["status"] = "collecting_info"
            return self._format_smart_prompt(session, missing_fields)
        else:
            # All info collected, show time slots
            session["status"] = "selecting_slot"
            return self._show_available_slots(session)

    def _format_smart_prompt(self, session: Dict, missing_fields: List[str]) -> str:
        """Smart prompt that doesn't re-ask for info unnecessarily"""
        collected_count = len(session["collected_data"])
        total_count = len([f for f, c in self.requirements.items() if c["required"]])

        # If we have some info, show progress
        if collected_count > 0:
            response = f"📋 Booking Progress ({collected_count}/{total_count} completed)\n\n"
            response += "✅ Information collected:\n"
            for field, value in session["collected_data"].items():
                field_prompt = self.requirements[field]["prompt"]
                response += f"   • {field_prompt.title()}: {value}\n"
            response += "\n"
        else:
            response = "📋 Let's start your booking!\n\n"

        # Smart prompt for missing info
        if len(missing_fields) == 1:
            field_config = self.requirements[missing_fields[0]]
            response += f"📝 Just need {field_config['prompt']} to complete your booking:"
        else:
            missing_prompts = [self.requirements[f]['prompt'] for f in missing_fields]
            response += f"📝 Still need: {', '.join(missing_prompts)}\n"
            response += f"Please provide {self.requirements[missing_fields[0]]['prompt']}:"

        return response
    
    def _show_available_slots(self, session: Dict) -> str:
        """Show available time slots"""
        collected = session["collected_data"]
        course_product = collected.get("course_product", "General")
        
        # Get available slots
        available = [slot for slot in self.available_slots if slot["available"]][:10]
        
        if not available:
            return "❌ No available time slots at the moment. Please try again later."
        
        response = f"""✅ All information collected! Here's your booking summary:

📋 Booking Details:
• Name: {collected['name']}
• Course/Product: {collected['course_product']}
• Email: {collected['email']}
• Phone: {collected['phone']}

📅 Available Time Slots:

"""
        
        # Format slots
        session["available_slots"] = {}
        slot_number = 1
        
        for slot in available:
            date_obj = datetime.strptime(slot["date"], "%Y-%m-%d")
            readable_date = date_obj.strftime("%A, %B %d, %Y")
            
            session["available_slots"][str(slot_number)] = slot
            response += f"   {slot_number}. {readable_date} at {slot['time']} - {slot['service_type']}\n"
            slot_number += 1
        
        response += f"\n💡 Please reply with the slot number (1-{slot_number-1}) to book your appointment."
        
        return response
    
    def _handle_slot_selection(self, slot_number: str, session: Dict, thread_id: str) -> str:
        """Handle time slot selection"""
        if session.get("status") != "selecting_slot":
            return "Please complete the booking information first before selecting a time slot."
        
        available_slots = session.get("available_slots", {})
        
        if slot_number not in available_slots:
            return f"❌ Invalid slot number. Please choose a number between 1 and {len(available_slots)}."
        
        selected_slot = available_slots[slot_number]
        
        # Create the booking
        return self._create_booking(session, selected_slot, thread_id)
    
    def _create_booking(self, session: Dict, selected_slot: Dict, thread_id: str) -> str:
        """Create the final booking"""
        collected = session["collected_data"]
        
        # Check if slot is still available
        for slot in self.available_slots:
            if (slot["date"] == selected_slot["date"] and 
                slot["time"] == selected_slot["time"] and 
                slot["service_type"] == selected_slot["service_type"] and 
                slot["available"]):
                # Mark as booked
                slot["available"] = False
                break
        else:
            return "❌ Sorry, this time slot is no longer available. Please select another slot."
        
        # Create booking record
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        booking_record = {
            "booking_id": booking_id,
            "thread_id": thread_id,
            "name": collected["name"],
            "course_product": collected["course_product"],
            "email": collected["email"],
            "phone": collected["phone"],
            "date": selected_slot["date"],
            "time": selected_slot["time"],
            "service_type": selected_slot["service_type"],
            "status": "confirmed",
            "created_at": datetime.now().isoformat()
        }
        
        self.booked_appointments.append(booking_record)
        
        # Update session
        session["status"] = "completed"
        session["booking_id"] = booking_id
        session["booking_record"] = booking_record
        session["updated_at"] = datetime.now().isoformat()
        
        # Format confirmation
        date_obj = datetime.strptime(selected_slot["date"], "%Y-%m-%d")
        readable_date = date_obj.strftime("%A, %B %d, %Y")
        
        confirmation = f"""
🎉 Booking Confirmed Successfully!

📋 Booking Details:
• Booking ID: {booking_id}
• Name: {collected['name']}
• Course/Product: {collected['course_product']}
• Email: {collected['email']}
• Phone: {collected['phone']}
• Date: {readable_date}
• Time: {selected_slot['time']}
• Service: {selected_slot['service_type']}

📧 A confirmation email will be sent to {collected['email']}.
📱 You'll receive an SMS reminder 24 hours before your appointment.

💡 To modify or cancel this booking, please contact us with your Booking ID: {booking_id}

Thank you for choosing our services! 🙏
"""
        
        logger.info(f"✅ Booking created: {booking_id} for thread: {thread_id}")
        
        return confirmation.strip()
    
    def _validate_field(self, field_name: str, value: str) -> Dict[str, Any]:
        """Validate field value"""
        if field_name not in self.requirements:
            return {"valid": False, "message": f"Unknown field: {field_name}"}
        
        if not value or not value.strip():
            return {"valid": False, "message": "Value cannot be empty"}
        
        value = value.strip()
        field_type = self.requirements[field_name]["type"]
        
        if field_type == "email":
            if "@" not in value or "." not in value:
                return {"valid": False, "message": "Please provide a valid email address"}
        elif field_type == "phone":
            clean_phone = re.sub(r'[\-\s\(\)]', '', value)
            if len(clean_phone) < 10:
                return {"valid": False, "message": "Please provide a valid phone number (at least 10 digits)"}
        
        return {"valid": True, "value": value}
    
    def _get_missing_fields(self, session: Dict) -> List[str]:
        """Get missing required fields"""
        collected = session["collected_data"]
        missing = []
        
        for field_name, config in self.requirements.items():
            if config["required"] and field_name not in collected:
                missing.append(field_name)
        
        return missing
    
    def _show_booking_status(self, session: Dict, thread_id: str) -> str:
        """Show current booking status"""
        if session["status"] == "completed":
            booking_record = session.get("booking_record")
            if booking_record:
                date_obj = datetime.strptime(booking_record["date"], "%Y-%m-%d")
                readable_date = date_obj.strftime("%A, %B %d, %Y")
                
                return f"""
📋 Your Current Booking:

• Booking ID: {booking_record['booking_id']}
• Name: {booking_record['name']}
• Course/Product: {booking_record['course_product']}
• Date: {readable_date}
• Time: {booking_record['time']}
• Status: {booking_record['status'].title()}

💡 To modify this booking, please let me know what you'd like to change.
"""
        
        elif session["status"] == "collecting_info":
            return self._format_collection_prompt(session, self._get_missing_fields(session)[0])
        
        elif session["status"] == "selecting_slot":
            return self._show_available_slots(session)
        
        else:
            return "No active booking found. Would you like to start a new booking?"
    
    def _update_information(self, field: str, value: str, session: Dict, thread_id: str) -> str:
        """Update existing information"""
        if field not in self.requirements:
            return f"❌ Cannot update {field}. Valid fields are: {', '.join(self.requirements.keys())}"
        
        validation = self._validate_field(field, value)
        if not validation["valid"]:
            return f"❌ {validation['message']}"
        
        old_value = session["collected_data"].get(field, "Not set")
        session["collected_data"][field] = validation["value"]
        session["updated_at"] = datetime.now().isoformat()
        
        return f"✅ Updated {field} from '{old_value}' to '{value}'.\n\n{self._show_booking_status(session, thread_id)}"
    
    def _confirm_booking(self, session: Dict, thread_id: str) -> str:
        """Confirm booking if ready"""
        if session["status"] == "completed":
            return "Your booking is already confirmed!"
        
        missing = self._get_missing_fields(session)
        if missing:
            return f"Cannot confirm booking yet. Still need: {', '.join([self.requirements[f]['prompt'] for f in missing])}"
        
        if session["status"] != "selecting_slot":
            session["status"] = "selecting_slot"
            return self._show_available_slots(session)
        
        return "Please select a time slot to confirm your booking."
    
    def _handle_unclear_intent(self, user_message: str, session: Dict, thread_id: str) -> str:
        """Handle unclear user intent"""
        if session["status"] == "new":
            return "I'd be happy to help you book an appointment! To get started, please provide your full name."
        
        elif session["status"] == "collecting_info":
            missing = self._get_missing_fields(session)
            if missing:
                field_config = self.requirements[missing[0]]
                return f"I need {field_config['prompt']} to continue with your booking. Could you please provide that?"
        
        elif session["status"] == "selecting_slot":
            return "Please select a time slot number from the available options above."
        
        else:
            return "I'm not sure what you'd like to do. Could you please clarify your booking request?"
    
    def _generate_mock_slots(self) -> List[Dict]:
        """Generate mock time slots"""
        slots = []
        start_date = datetime.now().date()
        
        for day_offset in range(1, 31):
            date = start_date + timedelta(days=day_offset)
            if date.weekday() >= 5:  # Skip weekends
                continue
            
            for hour in range(9, 17):
                if datetime.now().hour < hour or day_offset > 0:  # Only future slots
                    slots.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "time": f"{hour:02d}:00",
                        "service_type": "General Consultation",
                        "available": True
                    })
        
        return slots
