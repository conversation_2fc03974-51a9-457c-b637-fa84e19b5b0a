"""
Test script for Smart Booking System - Single intelligent tool
Demonstrates context-aware booking with natural conversation flow
"""

import logging
import sys
import os

# Add parent directory to path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent_v2.main_agent import MainAgentV2

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_smart_booking_system():
    """Test the smart booking system with natural conversation flow"""
    
    print("🎯 Testing Smart Booking System - Single Intelligent Tool")
    print("=" * 65)
    
    try:
        # Initialize the agent
        agent = MainAgentV2()
        print("✅ Agent V2 initialized successfully")
        
        # Test natural booking conversation
        thread_id = "smart_booking_test_1"
        
        print(f"\n💬 Natural Booking Conversation (Thread: {thread_id})")
        print("-" * 55)
        
        # Test 1: Start booking with natural language
        print("\n👤 User: I want to book an appointment for IELTS course")
        response = agent.chat("I want to book an appointment for IELTS course", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 2: Provide name naturally
        print("\n👤 User: My name is <PERSON>")
        response = agent.chat("My name is <PERSON> <PERSON>", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 3: Provide email
        print("\n👤 User: <EMAIL>")
        response = agent.chat("<EMAIL>", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 4: Provide phone
        print("\n👤 User: +977-9841234567")
        response = agent.chat("+977-9841234567", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 5: Select time slot
        print("\n👤 User: I'll take slot 2")
        response = agent.chat("I'll take slot 2", thread_id)
        print(f"🤖 Agent: {response}")
        
        print("\n" + "="*65)
        print("🧪 Testing Different Scenarios")
        print("-" * 55)
        
        # Test new conversation - mixed information
        thread_id_2 = "smart_booking_test_2"
        
        # Test 6: Provide multiple info at once
        print(f"\n👤 User (New Thread): I want to book German course, my name is John Doe and <NAME_EMAIL>")
        response = agent.chat("I want to book German course, my name is John Doe and <NAME_EMAIL>", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 7: Provide phone
        print("\n👤 User: My phone is 9876543210")
        response = agent.chat("My phone is 9876543210", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        # Test 8: Select slot with natural language
        print("\n👤 User: I prefer the first option")
        response = agent.chat("I prefer the first option", thread_id_2)
        print(f"🤖 Agent: {response}")
        
        print("\n" + "="*65)
        print("🔄 Testing Updates and Status")
        print("-" * 55)
        
        # Test new conversation - updates
        thread_id_3 = "smart_booking_test_3"
        
        # Test 9: Start booking
        print(f"\n👤 User (Thread 3): Book appointment")
        response = agent.chat("Book appointment", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 10: Provide name
        print("\n👤 User: Alice Smith")
        response = agent.chat("Alice Smith", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 11: Provide course
        print("\n👤 User: Python programming")
        response = agent.chat("Python programming", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 12: Invalid email
        print("\n👤 User: invalid-email")
        response = agent.chat("invalid-email", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 13: Valid email
        print("\n👤 User: <EMAIL>")
        response = agent.chat("<EMAIL>", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 14: Phone
        print("\n👤 User: ************")
        response = agent.chat("************", thread_id_3)
        print(f"🤖 Agent: {response}")
        
        # Test 15: Check status of first booking
        print(f"\n👤 User (Back to Thread 1): What's my booking status?")
        response = agent.chat("What's my booking status?", thread_id)
        print(f"🤖 Agent: {response}")
        
        print("\n✅ Smart Booking System testing completed!")
        print("\n🎯 Key features demonstrated:")
        print("   • ✅ Single intelligent booking tool")
        print("   • ✅ Natural conversation flow")
        print("   • ✅ Context-aware information extraction")
        print("   • ✅ Automatic intent recognition")
        print("   • ✅ Dynamic field validation")
        print("   • ✅ Session management per thread")
        print("   • ✅ Multiple info in single message")
        print("   • ✅ Natural language slot selection")
        print("   • ✅ Error handling and recovery")
        print("   • ✅ Booking status tracking")
        print("   • ✅ No complex workflow - just one smart tool!")
        
    except Exception as e:
        logger.error(f"❌ Error testing smart booking system: {e}")
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    test_smart_booking_system()
