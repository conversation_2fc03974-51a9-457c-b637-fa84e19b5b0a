"""
User Memory Management System
Stores user information, course selections, and booking history using LangChain memory
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import json

from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage

logger = logging.getLogger(__name__)


@dataclass
class UserInfo:
    """User information structure"""
    name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


@dataclass
class CourseSelection:
    """Course selection with timestamp"""
    course_name: str
    course_code: str
    selected_at: str
    status: str = "selected"  # selected, booked, completed, cancelled


@dataclass
class UserSession:
    """Complete user session data"""
    user_info: UserInfo
    course_selections: List[CourseSelection]
    booking_history: List[Dict[str, Any]]
    session_created: str
    last_updated: str


class UserMemoryManager:
    """
    Manages user information and course selections using LangChain memory
    """
    
    def __init__(self):
        """Initialize memory manager"""
        self.memory = MemorySaver()
        logger.info("✅ User Memory Manager initialized")
    
    def get_user_session(self, thread_id: str) -> UserSession:
        """
        Get or create user session data
        
        Args:
            thread_id: User's thread ID
            
        Returns:
            UserSession object with current data
        """
        try:
            config = {"configurable": {"thread_id": thread_id}}
            
            # Try to get existing state
            try:
                state = self.memory.get(config)
                if state and "user_session" in state:
                    session_data = json.loads(state["user_session"])
                    return self._dict_to_session(session_data)
            except Exception:
                pass  # Create new session if retrieval fails
            
            # Create new session
            now = datetime.now().isoformat()
            return UserSession(
                user_info=UserInfo(created_at=now, updated_at=now),
                course_selections=[],
                booking_history=[],
                session_created=now,
                last_updated=now
            )
            
        except Exception as e:
            logger.error(f"Error getting user session: {e}")
            # Return empty session as fallback
            now = datetime.now().isoformat()
            return UserSession(
                user_info=UserInfo(created_at=now, updated_at=now),
                course_selections=[],
                booking_history=[],
                session_created=now,
                last_updated=now
            )
    
    def save_user_session(self, thread_id: str, session: UserSession) -> bool:
        """
        Save user session data to memory
        
        Args:
            thread_id: User's thread ID
            session: UserSession object to save
            
        Returns:
            True if saved successfully
        """
        try:
            config = {"configurable": {"thread_id": thread_id}}
            session.last_updated = datetime.now().isoformat()
            
            # Convert to dict and save
            session_data = self._session_to_dict(session)
            
            # Save to memory using a simple message-based approach
            message = AIMessage(content=f"User session updated: {json.dumps(session_data)}")
            self.memory.put(config, {"messages": [message], "user_session": json.dumps(session_data)})
            
            logger.info(f"💾 User session saved for thread: {thread_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving user session: {e}")
            return False
    
    def update_user_info(self, thread_id: str, **kwargs) -> bool:
        """
        Update user information
        
        Args:
            thread_id: User's thread ID
            **kwargs: User info fields to update (name, phone, email)
            
        Returns:
            True if updated successfully
        """
        try:
            session = self.get_user_session(thread_id)
            
            # Update user info fields
            if 'name' in kwargs:
                session.user_info.name = kwargs['name']
            if 'phone' in kwargs:
                session.user_info.phone = kwargs['phone']
            if 'email' in kwargs:
                session.user_info.email = kwargs['email']
            
            session.user_info.updated_at = datetime.now().isoformat()
            
            return self.save_user_session(thread_id, session)
            
        except Exception as e:
            logger.error(f"Error updating user info: {e}")
            return False
    
    def add_course_selection(self, thread_id: str, course_name: str, course_code: str) -> bool:
        """
        Add a course selection with timestamp
        
        Args:
            thread_id: User's thread ID
            course_name: Name of the selected course
            course_code: Code of the selected course
            
        Returns:
            True if added successfully
        """
        try:
            session = self.get_user_session(thread_id)
            
            # Check if course already selected
            for selection in session.course_selections:
                if selection.course_code == course_code:
                    # Update existing selection
                    selection.selected_at = datetime.now().isoformat()
                    selection.status = "selected"
                    logger.info(f"📚 Updated course selection: {course_name}")
                    return self.save_user_session(thread_id, session)
            
            # Add new course selection
            new_selection = CourseSelection(
                course_name=course_name,
                course_code=course_code,
                selected_at=datetime.now().isoformat(),
                status="selected"
            )
            
            session.course_selections.append(new_selection)
            logger.info(f"📚 Added course selection: {course_name} ({course_code})")
            
            return self.save_user_session(thread_id, session)
            
        except Exception as e:
            logger.error(f"Error adding course selection: {e}")
            return False
    
    def get_user_courses(self, thread_id: str) -> List[CourseSelection]:
        """
        Get user's selected courses
        
        Args:
            thread_id: User's thread ID
            
        Returns:
            List of CourseSelection objects
        """
        try:
            session = self.get_user_session(thread_id)
            return session.course_selections
        except Exception as e:
            logger.error(f"Error getting user courses: {e}")
            return []
    
    def _session_to_dict(self, session: UserSession) -> Dict[str, Any]:
        """Convert UserSession to dictionary"""
        return {
            "user_info": asdict(session.user_info),
            "course_selections": [asdict(cs) for cs in session.course_selections],
            "booking_history": session.booking_history,
            "session_created": session.session_created,
            "last_updated": session.last_updated
        }
    
    def _dict_to_session(self, data: Dict[str, Any]) -> UserSession:
        """Convert dictionary to UserSession"""
        return UserSession(
            user_info=UserInfo(**data["user_info"]),
            course_selections=[CourseSelection(**cs) for cs in data["course_selections"]],
            booking_history=data["booking_history"],
            session_created=data["session_created"],
            last_updated=data["last_updated"]
        )


# Global instance
_user_memory_manager = None


def get_user_memory_manager() -> UserMemoryManager:
    """Get the global user memory manager instance"""
    global _user_memory_manager
    if _user_memory_manager is None:
        _user_memory_manager = UserMemoryManager()
    return _user_memory_manager
