"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging
import threading
from typing import Literal, Dict, Any
from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from langgraph.checkpoint.memory import MemorySaver

from utils import log_user_input, log_agent_response, log_tool_call, log_tool_result

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Thread-local storage for current thread_id context
_thread_local = threading.local()

def set_current_thread_id(thread_id: str):
    """Set the current thread ID for tool context"""
    _thread_local.thread_id = thread_id

def get_current_thread_id() -> str:
    """Get the current thread ID for tool context"""
    return getattr(_thread_local, 'thread_id', 'default')

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Global agent instances to maintain session state
_global_booking_agent = None
_global_search_agent = None

def get_booking_agent():
    """Get the global booking agent instance"""
    global _global_booking_agent
    if _global_booking_agent is None:
        from .smart_booking_agent import SmartBookingAgent
        _global_booking_agent = SmartBookingAgent()
    return _global_booking_agent

def get_search_agent():
    """Get the global search agent instance"""
    global _global_search_agent
    if _global_search_agent is None:
        from .search_agent import SearchAgentV2
        _global_search_agent = SearchAgentV2(tenant_id="ambition-guru")
    return _global_search_agent


@tool
def search_information(user_message: str) -> str:
    """
    Search for general information, troubleshooting, apps, and services.
    
    Args:
        user_message: The user's exact message for information search
        
    Returns:
        Search results for information/troubleshooting queries
    """
    log_tool_call("search_information", f"user_message='{user_message}'")
    
    try:
        search_agent = get_search_agent()
        thread_id = get_current_thread_id()
        result = search_agent.search_information(user_message, thread_id)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in information search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool  
def search_products(user_message: str) -> str:
    """
    Search for products, courses, and educational programs.
    
    Args:
        user_message: The user's exact message for product search
        
    Returns:
        Search results for courses/products
    """
    log_tool_call("search_products", f"user_message='{user_message}'")
    
    try:
        search_agent = get_search_agent()
        thread_id = get_current_thread_id()
        result = search_agent.search_products(user_message, thread_id)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error in product search: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def verify_course_exists(course_name: str) -> str:
    """
    Verify if a course exists in the product catalog

    Args:
        course_name: Name of the course to verify

    Returns:
        Course verification result with details
    """
    log_tool_call("verify_course_exists", f"course_name='{course_name}'")

    try:
        search_agent = get_search_agent()
        # Use search_products to verify course existence
        result = search_agent.search_products(f"verify {course_name}", "default")
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error verifying course: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def handle_booking(user_message: str, thread_id: str = "default") -> str:
    """
    Intelligent booking handler that understands context and manages the entire booking process.

    This single tool handles all booking scenarios:
    - Starting new bookings
    - Collecting required information (name, course/product, email, phone)
    - Showing available time slots
    - Confirming bookings
    - Updating existing bookings
    - Showing booking status

    Args:
        user_message: The user's exact message about booking
        thread_id: Thread identifier for session management

    Returns:
        Appropriate response based on booking context and user intent
    """
    log_tool_call("handle_booking", f"user_message='{user_message}', thread_id='{thread_id}'")

    try:
        booking_agent = get_booking_agent()
        current_thread_id = get_current_thread_id()
        result = booking_agent.handle_booking_request(user_message, current_thread_id)
        log_tool_result(result)
        return result
    except Exception as e:
        error_msg = f"Error handling booking: {str(e)}"
        logger.error(error_msg)
        return error_msg


# Define tools list
tools = [search_information, search_products, verify_course_exists, handle_booking]

# Create memory for conversation persistence using LangGraph's built-in patterns
memory = MemorySaver()

MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

You MUST use the appropriate tools for every user request. Do NOT try to answer questions without using tools.

MANDATORY TOOL USAGE RULES:
1. If user asks about courses, programs, education, or "what courses" → ALWAYS use search_products tool
2. If user asks about troubleshooting, apps, problems, or technical issues → ALWAYS use search_information tool
3. If user mentions booking, scheduling, or wants to book something → FIRST use search_products to verify the course exists, THEN use handle_booking tool
4. ONLY respond directly for simple greetings like "hello", "hi", "namaste"

EXAMPLES:
- "what courses do you have?" → MUST use search_products
- "show me available courses" → MUST use search_products
- "I want to book Python course" → FIRST use search_products to verify Python course exists, THEN use handle_booking
- "book Data Science class" → FIRST use search_products to check if Data Science course is available
- "app not working" → MUST use search_information
- "hello" → respond directly

IMPORTANT: Always verify course availability with search_products before booking!

You have these tools available:
- search_products: Returns available courses and programs
- search_information: Returns troubleshooting and general information
- verify_course_exists: Verifies if a specific course exists in catalog
- handle_booking: Manages the complete booking process

CRITICAL: Always use tools when appropriate. Never try to guess or make up information about courses or services."""


class MainAgentV2:
    """
    Main Agent V2 - Modern LangChain implementation using LangGraph
    Uses LangGraph's built-in persistence and memory management
    """

    def __init__(self):
        """Initialize the agent using the official LangChain pattern"""
        self.memory = memory
        self.llm = main_llm
        self.tools = tools

        # Use the official LangChain pattern from the documentation
        from langgraph.prebuilt import create_react_agent
        from langchain_core.prompts import ChatPromptTemplate

        # Create a proper prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT),
            ("placeholder", "{messages}"),
        ])

        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            checkpointer=self.memory,
            prompt=prompt
        )
        logger.info("✅ Main Agent V2 initialized with official LangGraph pattern")

    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response using LangGraph
        
        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management
            
        Returns:
            Agent's response
        """
        log_user_input(message)

        # Set thread context for tools
        set_current_thread_id(thread_id)

        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}

        try:
            # Invoke the agent with the message
            response = self.agent.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # Extract the final response
            final_response = response["messages"][-1].content
            log_agent_response(final_response)
            
            # Log tool usage for debugging
            tool_calls_made = []
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_calls_made.append(tool_call['name'])
            
            if tool_calls_made:
                logger.info(f"🔧 TOOLS USED: {tool_calls_made}")
            else:
                logger.warning("⚠️  NO TOOLS USED - Possible hallucination!")
            
            return final_response
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def stream_chat(self, message: str, thread_id: str = "default"):
        """
        Stream the agent's response for real-time interaction
        
        Args:
            message: User's message
            thread_id: Conversation thread ID
            
        Yields:
            Streaming response chunks
        """
        log_user_input(message)
        
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            for step in self.agent.stream(
                {"messages": [HumanMessage(content=message)]},
                config=config,
                stream_mode="values"
            ):
                yield step["messages"][-1]
                
        except Exception as e:
            error_msg = f"Error in streaming: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """
        Get conversation history for a thread using LangGraph's memory
        
        Args:
            thread_id: Conversation thread ID
            
        Returns:
            List of messages in the conversation
        """
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Get the current state which includes message history
            state = self.agent.get_state(config)
            return state.values.get("messages", [])
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
