"""
Search Agent V2 - Modern LangChain implementation with proper memory management
Uses LangGraph's built-in memory patterns instead of hardcoded context handling
"""

import logging
from typing import Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>plate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
import os
from dotenv import load_dotenv

# from config import get_vector_store_manager

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """
    Search Agent V2 - Modern implementation using LangGraph memory patterns
    Replaces hardcoded context handling with proper LangChain memory management
    """
    
    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize with mock data for testing"""
        self.tenant_id = tenant_id

        # Initialize LLM for query translation
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Create memory for conversation context using LangGraph patterns
        self.memory = MemorySaver()

        # Mock data for testing
        self.mock_courses = [
            "Python Programming Fundamentals",
            "Web Development with React",
            "Data Science and Machine Learning",
            "Digital Marketing Mastery",
            "Business Analytics",
            "Mobile App Development",
            "Cybersecurity Essentials",
            "Cloud Computing with AWS"
        ]

        self.mock_info = [
            "Our platform offers 24/7 customer support",
            "You can access courses on mobile and desktop",
            "All courses include certificates upon completion",
            "We offer flexible payment plans",
            "Live instructor support is available"
        ]

        logger.info("✅ Search Agent V2 initialized with mock data for testing")
    
    def _setup_query_translator(self):
        """Setup LangGraph workflow for intelligent query translation - DISABLED FOR TESTING"""
        pass
        # Commented out for testing - using simple mock responses instead
        # # Define the state schema for query translation
        # workflow = StateGraph(state_schema=MessagesState)
        
        def translate_query(state: MessagesState):
            """Translate user query based on conversation context"""
            
            # Get conversation history from state
            messages = state["messages"]
            current_message = messages[-1].content if messages else ""
            
            # Create context from recent messages
            context_messages = messages[-5:] if len(messages) > 1 else []
            context_text = " ".join([msg.content for msg in context_messages[:-1]])
            
            # System prompt for query translation
            system_prompt = """You are a query translator that converts user messages into effective search queries.

Based on the conversation context and current message, determine:
1. The best search query to use
2. Whether this is an "information" search (troubleshooting, apps, services) or "products" search (courses, programs)

Context analysis rules:
- If previous messages mention problems ("chalena", "not working", "issue", "error"), treat as troubleshooting
- If user mentions specific apps like "Ambition Guru" with problem context, focus on that app's troubleshooting
- If asking about courses without problem context, treat as product search
- If asking "what is" about an app mentioned before, provide app information

Current message: {current_message}
Recent conversation context: {context}

Respond with:
1. Translated query (optimized for search)
2. Search type: "information" or "products"
3. Brief reasoning

Format: 
Query: [your translated query]
Type: [information/products]
Reasoning: [brief explanation]"""

            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", "Translate this query: {current_message}\nContext: {context}")
            ])
            
            # Get translation
            response = self.llm.invoke(
                prompt.format_messages(
                    current_message=current_message,
                    context=context_text
                )
            )
            
            return {"messages": [response]}
        
        # Add node and compile
        workflow.add_node("translate", translate_query)
        workflow.add_edge(START, "translate")
        
        self.query_translator = workflow.compile(checkpointer=self.memory)
    
    def _translate_query_with_context(self, user_message: str, search_type: str, thread_id: str = "default") -> tuple[str, str]:
        """
        Translate user message using LangGraph memory for context awareness
        
        Args:
            user_message: Raw user input
            search_type: Intended search type
            thread_id: Thread ID for memory context
            
        Returns:
            Tuple of (translated_query, corrected_search_type)
        """
        # Simple mock implementation for testing
        logger.info(f"🔄 Mock query translation: '{user_message}' (type: {search_type})")
        return user_message, search_type
    
    def _simple_query_translation(self, user_message: str, search_type: str) -> tuple[str, str]:
        """Fallback simple query translation without context"""
        user_lower = user_message.lower()
        
        # Problem detection
        if any(word in user_lower for word in ["chalena", "not working", "problem", "issue", "error"]):
            if "ambition guru" in user_lower or "ambition" in user_lower:
                return f"Ambition Guru app troubleshooting problems not working", "information"
            else:
                return f"troubleshooting problems not working {user_message}", "information"
        
        # Course queries
        if search_type == "products" and any(word in user_lower for word in ["course", "kors", "kun kun"]):
            return f"available courses programs {user_message}", "products"
        
        # Default
        return f"{search_type} {user_message}", search_type

    def _search_with_retriever(self, search_query: str, search_type: str) -> str:
        """
        DISABLED - Using mock data instead
        """
        # This method is disabled for testing - using mock responses in search methods
        return "Mock search disabled"

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for general information using mock data

        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context

        Returns:
            Formatted search results
        """
        logger.info(f"📋 Information search: '{user_message}'")

        # Return mock information
        info_results = "\n".join([f"• {info}" for info in self.mock_info])

        return f"""Here's some helpful information about our services:

{info_results}

Is there anything specific you'd like to know more about?"""
    
    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for products using mock data

        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context

        Returns:
            Formatted search results
        """
        logger.info(f"🎓 Products search: '{user_message}'")

        # Return mock courses
        course_results = "\n".join([f"• {course}" for course in self.mock_courses])

        return f"""Here are our available courses:

{course_results}

All courses include:
- Certificate upon completion
- 24/7 access to materials
- Live instructor support
- Flexible payment options

Would you like to book any of these courses or need more information about a specific one?"""
    
    def get_memory_stats(self, thread_id: str = "default") -> Dict[str, Any]:
        """Get memory statistics for debugging"""
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            state = self.query_translator.get_state(config)
            messages = state.values.get("messages", [])
            
            return {
                "thread_id": thread_id,
                "message_count": len(messages),
                "last_message": messages[-1].content if messages else None,
                "memory_type": "LangGraph MemorySaver"
            }
        except Exception as e:
            return {
                "thread_id": thread_id,
                "error": str(e)
            }
