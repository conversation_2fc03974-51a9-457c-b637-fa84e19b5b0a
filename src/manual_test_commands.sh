#!/bin/bash

# Manual Testing Commands for Chat API
# Run these commands one by one to test different scenarios

TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInVzZXJfaWQiOiI2ODZhMDIxNzllOGVlZTBkOWYyYzVkYjgiLCJyb2xlIjoiYWRtaW4iLCJ0ZW5hbnRfaWQiOiI2ODZhMDIxNzllOGVlZTBkOWYyYzVkYjYiLCJleHAiOjE3NTE3OTk0MjF9.Z9jxsraEBqUKnDDpeTfVGzhaECMgNOWaR0hN-dwh43c"

echo "🧪 COMPREHENSIVE CHAT TESTING COMMANDS"
echo "Copy and paste these commands one by one"
echo "========================================"

echo ""
echo "1️⃣ TEST: Search Information Queries"
echo "-----------------------------------"

echo "# Test customer support info"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What support do you provide?\"}'"

echo ""
echo "# Test payment options"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What are your payment options?\"}'"

echo ""
echo "# Test certificate info"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What certificates do you provide?\"}'"

echo ""
echo "2️⃣ TEST: Wrong Course Name Detection"
echo "-----------------------------------"

echo "# Test non-existent Python course"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I want to book Python programming course\"}'"

echo ""
echo "# Test non-existent Java course"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"Do you have Java course?\"}'"

echo ""
echo "# Test non-existent Machine Learning"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I need Machine Learning course\"}'"

echo ""
echo "3️⃣ TEST: Correct Course Booking"
echo "------------------------------"

echo "# Test SEE Bridge Course"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I want to book SEE Bridge Course\"}'"

echo ""
echo "# Test IELTS Preparation"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"Do you have IELTS preparation?\"}'"

echo ""
echo "# Test Korean Language"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I need Korean language course\"}'"

echo ""
echo "4️⃣ TEST: Complete Booking Flow"
echo "-----------------------------"

echo "# Start booking IELTS"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I want to book IELTS Preparation course\"}'"

echo ""
echo "# Provide name"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"My name is Ram Sharma\"}'"

echo ""
echo "# Provide phone"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"My phone number is 9841234567\"}'"

echo ""
echo "# Provide email"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"My <NAME_EMAIL>\"}'"

echo ""
echo "5️⃣ TEST: Multiple Course Interest"
echo "--------------------------------"

echo "# Ask about Korean courses"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What Korean language courses do you have?\"}'"

echo ""
echo "# Show interest in Korean Level 1"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I am interested in Korean Level 1\"}'"

echo ""
echo "# Ask about German courses too"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I also want to know about German courses\"}'"

echo ""
echo "# Want both courses"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I want both Korean Level 1 and German A1\"}'"

echo ""
echo "6️⃣ TEST: Course Name Changes"
echo "---------------------------"

echo "# Start with BBS"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"I want to book BBS course\"}'"

echo ""
echo "# Change to BBA"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"Actually, change that to BBA course\"}'"

echo ""
echo "# Change to CSIT"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"No wait, I prefer CSIT entrance preparation\"}'"

echo ""
echo "7️⃣ TEST: Memory and Context"
echo "--------------------------"

echo "# Check what user info is remembered"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What information do you have about me?\"}'"

echo ""
echo "# Check course interests"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"What courses am I interested in?\"}'"

echo ""
echo "8️⃣ TEST: Show All Available Courses"
echo "----------------------------------"

echo "# List all courses"
echo "curl -X POST \"http://localhost:8001/api/v1/chat\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer $TOKEN\" \\"
echo "  -d '{\"message\": \"Show me all available courses\"}'"

echo ""
echo "========================================"
echo "🎉 END OF TESTING COMMANDS"
echo "Copy and run each command to test the system!"
