// API Configuration
const API_BASE_URL = 'http://localhost:8000';

// Global state
let authToken = null;
let currentUser = null;

// DOM Elements
const loginContainer = document.getElementById('loginContainer');
const chatContainer = document.getElementById('chatContainer');
const loginForm = document.getElementById('loginForm');
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const loadingOverlay = document.getElementById('loadingOverlay');
const toastContainer = document.getElementById('toastContainer');
const logoutBtn = document.getElementById('logoutBtn');
const userWelcome = document.getElementById('userWelcome');
const userRole = document.getElementById('userRole');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Check for existing token
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('currentUser');
    
    if (savedToken && savedUser) {
        authToken = savedToken;
        currentUser = JSON.parse(savedUser);
        showChatInterface();
    }
    
    // Event listeners
    setupEventListeners();
}

function setupEventListeners() {
    // Login form
    loginForm.addEventListener('submit', handleLogin);
    
    // Demo account buttons
    document.querySelectorAll('.demo-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const username = this.dataset.username;
            const password = this.dataset.password;
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            showToast('Demo credentials filled!', 'success');
        });
    });
    
    // Chat functionality
    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // Quick action buttons
    document.querySelectorAll('.quick-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const message = this.dataset.message;
            messageInput.value = message;
            sendMessage();
        });
    });
    
    // Logout
    logoutBtn.addEventListener('click', handleLogout);
}

// Authentication
async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('grant_type', 'password');
    formData.append('username', document.getElementById('username').value);
    formData.append('password', document.getElementById('password').value);
    formData.append('client_id', document.getElementById('tenant').value);
    formData.append('scope', '');
    
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/login`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;
            currentUser = {
                username: data.username,
                role: data.role,
                tenant: data.tenant_label
            };
            
            // Save to localStorage
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            showToast('Login successful!', 'success');
            showChatInterface();
        } else {
            const error = await response.text();
            showToast('Login failed: ' + error, 'error');
        }
    } catch (error) {
        showToast('Network error: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

function handleLogout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    
    // Reset form
    loginForm.reset();
    
    // Clear chat messages (keep welcome message)
    const welcomeMessage = chatMessages.querySelector('.welcome-message');
    chatMessages.innerHTML = '';
    chatMessages.appendChild(welcomeMessage);
    
    showLoginInterface();
    showToast('Logged out successfully!', 'success');
}

// UI Management
function showLoginInterface() {
    loginContainer.style.display = 'flex';
    chatContainer.style.display = 'none';
}

function showChatInterface() {
    loginContainer.style.display = 'none';
    chatContainer.style.display = 'flex';
    
    // Update user info
    userWelcome.textContent = `Welcome, ${currentUser.username}!`;
    userRole.textContent = currentUser.role.toUpperCase();
    
    // Focus on message input
    messageInput.focus();
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

// Chat functionality
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Clear input
    messageInput.value = '';
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Show typing indicator
    showTypingIndicator();
    
    // Disable send button
    sendBtn.disabled = true;
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({ message })
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Remove typing indicator
            removeTypingIndicator();
            
            // Add bot response
            addMessage(data.response, 'bot');
        } else {
            removeTypingIndicator();
            if (response.status === 401) {
                showToast('Session expired. Please login again.', 'error');
                handleLogout();
            } else {
                showToast('Failed to send message', 'error');
            }
        }
    } catch (error) {
        removeTypingIndicator();
        showToast('Network error: ' + error.message, 'error');
    } finally {
        sendBtn.disabled = false;
        messageInput.focus();
    }
}

function addMessage(content, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    const avatar = document.createElement('div');
    avatar.className = type === 'user' ? 'user-avatar' : 'bot-avatar';
    avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'typing-indicator';
    typingDiv.id = 'typingIndicator';
    
    const avatar = document.createElement('div');
    avatar.className = 'bot-avatar';
    avatar.innerHTML = '<i class="fas fa-robot"></i>';
    
    const dotsDiv = document.createElement('div');
    dotsDiv.className = 'typing-dots';
    dotsDiv.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
    
    typingDiv.appendChild(avatar);
    typingDiv.appendChild(dotsDiv);
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Toast notifications
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    toastContainer.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.style.animation = 'toastSlide 0.5s ease-out reverse';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 500);
    }, 3000);
}

// Utility functions
function formatTime(date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Auto-resize message input
messageInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
});

// Prevent form submission on Enter in message input
messageInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.shiftKey) {
        // Allow Shift+Enter for new lines
        return;
    }
});

// Add some visual feedback for button interactions
document.addEventListener('click', function(e) {
    if (e.target.matches('button') || e.target.closest('button')) {
        const button = e.target.matches('button') ? e.target : e.target.closest('button');
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }
});

// Handle connection errors gracefully
window.addEventListener('online', function() {
    showToast('Connection restored!', 'success');
});

window.addEventListener('offline', function() {
    showToast('Connection lost. Please check your internet.', 'error');
});
