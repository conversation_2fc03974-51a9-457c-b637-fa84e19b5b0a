<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ambition Guru - AI Customer Service</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Background Animation -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- Login Container -->
    <div id="loginContainer" class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-graduation-cap"></i>
                    <h1>Ambition Guru</h1>
                </div>
                <p class="subtitle">AI-Powered Customer Service</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        Username
                    </label>
                    <input type="text" id="username" name="username" required placeholder="Enter your username">
                </div>
                
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                </div>
                
                <div class="form-group">
                    <label for="tenant">
                        <i class="fas fa-building"></i>
                        Organization
                    </label>
                    <select id="tenant" name="tenant" required>
                        <option value="ambition-guru">Ambition Guru</option>
                    </select>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">Sign In</span>
                    <i class="fas fa-arrow-right"></i>
                </button>
            </form>
            
            <div class="demo-accounts">
                <p>Demo Accounts:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" data-username="admin" data-password="admin123">
                        <i class="fas fa-crown"></i> Admin
                    </button>
                    <button class="demo-btn" data-username="supervisor" data-password="supervisor123">
                        <i class="fas fa-user-tie"></i> Supervisor
                    </button>
                    <button class="demo-btn" data-username="agent1" data-password="agent123">
                        <i class="fas fa-headset"></i> Agent
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div id="chatContainer" class="chat-container" style="display: none;">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="chat-header-left">
                <div class="logo-small">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Ambition Guru</span>
                </div>
                <div class="user-info">
                    <span id="userWelcome">Welcome!</span>
                    <span id="userRole" class="user-role"></span>
                </div>
            </div>
            <div class="chat-header-right">
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <h3>👋 Welcome to Ambition Guru AI Assistant!</h3>
                    <p>I'm here to help you with:</p>
                    <ul>
                        <li>🎓 Course information and enrollment</li>
                        <li>📅 Class scheduling and bookings</li>
                        <li>🔧 Technical support and troubleshooting</li>
                        <li>❓ General questions and guidance</li>
                    </ul>
                    <p>How can I assist you today?</p>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <input type="text" id="messageInput" placeholder="Type your message here..." autocomplete="off">
                <button id="sendBtn" class="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="quick-actions">
                <button class="quick-btn" data-message="What courses do you offer?">
                    <i class="fas fa-book"></i> Courses
                </button>
                <button class="quick-btn" data-message="I want to book a demo class">
                    <i class="fas fa-calendar"></i> Book Demo
                </button>
                <button class="quick-btn" data-message="My app is not working">
                    <i class="fas fa-tools"></i> Support
                </button>
                <button class="quick-btn" data-message="What are your timings?">
                    <i class="fas fa-clock"></i> Timings
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Connecting to AI Assistant...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="script.js"></script>
</body>
</html>
