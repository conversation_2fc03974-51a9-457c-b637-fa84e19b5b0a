# Multi-Tenant Customer Service Frontend

A beautiful, modern frontend for the Multi-Tenant Customer Service API with rich animations and responsive design.

## 🎨 Features

- **Beautiful Login Interface** with animated background
- **Rich Chat Interface** with typing indicators
- **Responsive Design** for all devices
- **Demo Account <PERSON><PERSON>** for easy testing
- **Real-time Messaging** with the AI agent
- **Toast Notifications** for user feedback
- **Session Management** with localStorage
- **Quick Action Buttons** for common queries

## 🚀 Quick Start

### 1. Ensure API is Running
Make sure the FastAPI backend is running on `http://localhost:8000`

```bash
cd ../src
python main.py
```

### 2. Open Frontend
Simply open `index.html` in your web browser or serve it with a local server:

```bash
# Using Python
python -m http.server 8080

# Using Node.js
npx serve .

# Or just open index.html directly in your browser
```

### 3. Login
Use one of the demo accounts:
- **Admin**: admin / admin123
- **Supervisor**: supervisor / supervisor123  
- **Agent**: agent1 / agent123

## 🎯 Demo Accounts

Click the demo buttons on the login page to auto-fill credentials:

| Role | Username | Password | Access |
|------|----------|----------|---------|
| 👑 Admin | admin | admin123 | Full access |
| 👔 Supervisor | supervisor | supervisor123 | Limited admin |
| 🎧 Agent | agent1 | agent123 | Basic access |

## 💬 Chat Features

### Quick Actions
- 📚 **Courses** - "What courses do you offer?"
- 📅 **Book Demo** - "I want to book a demo class"
- 🔧 **Support** - "My app is not working"
- 🕐 **Timings** - "What are your timings?"

### Chat Capabilities
- Course information and enrollment
- Class scheduling and bookings
- Technical support and troubleshooting
- General questions and guidance

## 🎨 Design Features

### Visual Elements
- **Gradient Backgrounds** with floating animations
- **Glass Morphism** effects with backdrop blur
- **Smooth Animations** for all interactions
- **Typing Indicators** for realistic chat feel
- **Toast Notifications** for user feedback

### Responsive Design
- **Mobile-first** approach
- **Tablet** optimized layouts
- **Desktop** enhanced experience
- **Touch-friendly** buttons and inputs

## 🔧 Configuration

### API Endpoint
Update the API base URL in `script.js`:

```javascript
const API_BASE_URL = 'http://localhost:8000';
```

### Tenant Configuration
The frontend is configured for the "ambition-guru" tenant. To change:

1. Update the select option in `index.html`
2. Update the OAuth configuration in the backend

## 🌟 User Experience

### Login Flow
1. **Beautiful landing page** with animated background
2. **Demo account buttons** for quick testing
3. **Form validation** with visual feedback
4. **Loading states** during authentication
5. **Success/error notifications**

### Chat Experience
1. **Welcome message** with feature overview
2. **Real-time messaging** with typing indicators
3. **Quick action buttons** for common queries
4. **Message history** preserved during session
5. **Responsive design** for all devices

## 🎭 Animations

- **Floating shapes** in background
- **Slide-up** login card animation
- **Message slide-in** animations
- **Button hover** effects
- **Typing indicator** dots animation
- **Toast slide-in** notifications

## 📱 Mobile Experience

- **Touch-optimized** interface
- **Responsive** chat bubbles
- **Mobile-friendly** quick actions
- **Optimized** keyboard handling
- **Smooth scrolling** chat history

## 🔒 Security Features

- **JWT token** storage in localStorage
- **Automatic logout** on token expiration
- **Session persistence** across browser refreshes
- **Secure API** communication with Bearer tokens

## 🎨 Customization

### Colors
The design uses a purple gradient theme. To customize:

1. Update CSS variables in `styles.css`
2. Modify gradient colors in the background animation
3. Change button and accent colors

### Branding
1. Update logo and company name in `index.html`
2. Modify the welcome message content
3. Customize the chat interface branding

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the backend has proper CORS configuration
2. **API Connection**: Check that the backend is running on port 8000
3. **Login Issues**: Verify demo account credentials
4. **Chat Not Working**: Check browser console for errors

### Browser Compatibility

- **Chrome** 80+ ✅
- **Firefox** 75+ ✅
- **Safari** 13+ ✅
- **Edge** 80+ ✅

## 📄 Files Structure

```
frontend/
├── index.html          # Main HTML file
├── styles.css          # All CSS styles and animations
├── script.js           # JavaScript functionality
└── README.md          # This file
```

## 🚀 Production Deployment

For production deployment:

1. **Update API URL** to production endpoint
2. **Minify CSS/JS** files for better performance
3. **Add HTTPS** for secure communication
4. **Configure CDN** for static assets
5. **Add error tracking** (e.g., Sentry)

## 🤝 Contributing

1. Follow the existing design patterns
2. Maintain responsive design principles
3. Add smooth animations for new features
4. Test on multiple devices and browsers
5. Update documentation for new features
