{"cells": [{"cell_type": "code", "execution_count": 5, "id": "2b745791", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.environ.get(\"OPENAI_API_KEY\"):\n", "  os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter API key for OpenAI: \")\n", "\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-large\",dimensions=1536)"]}, {"cell_type": "code", "execution_count": 7, "id": "48a19108", "metadata": {}, "outputs": [], "source": ["from langchain_qdrant import QdrantVectorStore\n", "from qdrant_client import QdrantClient\n", "from qdrant_client.http.models import Distance, VectorParams\n", "\n", "client = QdrantClient(\n", "    url=\"http://172.16.16.148:6333\",\n", ")\n", "\n", "\n", "# client.create_collection(\n", "#     collection_name=\"langchain_products\",\n", "#     vectors_config=VectorParams(size=1536, distance=Distance.COSINE),\n", "# )\n", "\n", "vector_store = QdrantVectorStore(\n", "    client=client,\n", "    collection_name=\"langchain_products\",\n", "    embedding=embeddings,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "52fa51b7", "metadata": {}, "outputs": [], "source": ["products = [\n", "    {\n", "        \"name\": \"SEE Bridge Course\",\n", "        \"code\": \"SEE-BRIDGE\",\n", "        \"description\": \"Intensive course to prepare students transitioning from Grade 10 (SEE) to +2 level.\"\n", "    },\n", "    {\n", "        \"name\": \"BBS 1st Semester\",\n", "        \"code\": \"BBS-SEM1\",\n", "        \"description\": \"Bachelor of Business Studies first semester course under Tribhuvan University.\"\n", "    },\n", "    {\n", "        \"name\": \"BBA 1st Semester\",\n", "        \"code\": \"BBA-SEM1\",\n", "        \"description\": \"Bachelor of Business Administration first semester course for management students.\"\n", "    },\n", "    {\n", "        \"name\": \"CSIT Entrance Prep\",\n", "        \"code\": \"CSIT-ENT\",\n", "        \"description\": \"Preparation course for BSc CSIT entrance exam under Tribhuvan University.\"\n", "    },\n", "    {\n", "        \"name\": \"IELTS Preparation\",\n", "        \"code\": \"IELTS-PREP\",\n", "        \"description\": \"English proficiency test preparation course for international study or migration.\"\n", "    },\n", "    {\n", "        \"name\": \"Korean Language - TOPIK Level 1\",\n", "        \"code\": \"KOR-L1\",\n", "        \"description\": \"Beginner-level Korean language course targeting TOPIK Level 1 exam.\"\n", "    },\n", "    {\n", "        \"name\": \"Korean Language - TOPIK Level 2\",\n", "        \"code\": \"KOR-L2\",\n", "        \"description\": \"Intermediate Korean language course targeting TOPIK Level 2 for EPS Korea.\"\n", "    },\n", "    {\n", "        \"name\": \"German Language - A1 Level\",\n", "        \"code\": \"GER-A1\",\n", "        \"description\": \"Basic German language course aligned with CEFR A1 level for study/work abroad.\"\n", "    },\n", "    {\n", "        \"name\": \"German Language - B1 Level\",\n", "        \"code\": \"GER-B1\",\n", "        \"description\": \"Intermediate German language course (CEFR B1) for higher-level proficiency.\"\n", "    },\n", "    {\n", "        \"name\": \"EIS Grade 11\",\n", "        \"code\": \"EIS-G11\",\n", "        \"description\": \"Education in Science Grade 11 curriculum focused on core science subjects.\"\n", "    },\n", "    {\n", "        \"name\": \"WWIS Grade 12\",\n", "        \"code\": \"WWIS-G12\",\n", "        \"description\": \"World Wide Information Science Grade 12 content for IT and general knowledge.\"\n", "    },\n", "    {\n", "        \"name\": \"NASU Level 1 Preparation\",\n", "        \"code\": \"NASU-L1\",\n", "        \"description\": \"Preparation for NASU Level 1 government exam including general knowledge and aptitude.\"\n", "    },\n", "    {\n", "        \"name\": \"NIMABI Exam Prep\",\n", "        \"code\": \"NIMABI-PREP\",\n", "        \"description\": \"Training for the Nepal Madarsa Board (NIMABI) exams for Islamic education.\"\n", "    },\n", "]\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a6a69293", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "docs=[]\n", "for product in products:\n", "    docs.append(Document(page_content=product[\"description\"], metadata={\"name\": product[\"name\"], \"code\": product[\"code\"]}))"]}, {"cell_type": "code", "execution_count": 10, "id": "ea5a455e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'name': 'SEE Bridge Course', 'code': 'SEE-BRIDGE'}, page_content='Intensive course to prepare students transitioning from Grade 10 (SEE) to +2 level.'),\n", " Document(metadata={'name': 'BBS 1st Semester', 'code': 'BBS-SEM1'}, page_content='Bachelor of Business Studies first semester course under Tribhuvan University.'),\n", " Document(metadata={'name': 'BBA 1st Semester', 'code': 'BBA-SEM1'}, page_content='Bachelor of Business Administration first semester course for management students.'),\n", " Document(metadata={'name': 'CSIT Entrance Prep', 'code': 'CSIT-ENT'}, page_content='Preparation course for BSc CSIT entrance exam under Tribhuvan University.'),\n", " Document(metadata={'name': 'IELTS Preparation', 'code': 'IELTS-PREP'}, page_content='English proficiency test preparation course for international study or migration.'),\n", " Document(metadata={'name': 'Korean Language - TOPIK Level 1', 'code': 'KOR-L1'}, page_content='Beginner-level Korean language course targeting TOPIK Level 1 exam.'),\n", " Document(metadata={'name': 'Korean Language - TOPIK Level 2', 'code': 'KOR-L2'}, page_content='Intermediate Korean language course targeting TOPIK Level 2 for EPS Korea.'),\n", " Document(metadata={'name': 'German Language - A1 Level', 'code': 'GER-A1'}, page_content='Basic German language course aligned with CEFR A1 level for study/work abroad.'),\n", " Document(metadata={'name': 'German Language - B1 Level', 'code': 'GER-B1'}, page_content='Intermediate German language course (CEFR B1) for higher-level proficiency.'),\n", " Document(metadata={'name': 'EIS Grade 11', 'code': 'EIS-G11'}, page_content='Education in Science Grade 11 curriculum focused on core science subjects.'),\n", " Document(metadata={'name': 'WWIS Grade 12', 'code': 'WWIS-G12'}, page_content='World Wide Information Science Grade 12 content for IT and general knowledge.'),\n", " Document(metadata={'name': 'NASU Level 1 Preparation', 'code': 'NASU-L1'}, page_content='Preparation for NASU Level 1 government exam including general knowledge and aptitude.'),\n", " Document(metadata={'name': 'NIMABI Exam Prep', 'code': 'NIMABI-PREP'}, page_content='Training for the Nepal Madarsa Board (NIMABI) exams for Islamic education.')]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "code", "execution_count": 11, "id": "92fe3c49", "metadata": {}, "outputs": [{"data": {"text/plain": ["['3c9324ca19b04b09a26159dd84a3b820',\n", " '72125058bdfe45f9aa1f30b9ea8c28b9',\n", " '1b57a164ba4b4bbbb4c3f6014c915061',\n", " '527f0afd38ef4511902f9a4466cf574b',\n", " '55be21460d694b1dafbc7fcd768dd4f3',\n", " '5e403fc77be74ff9a3ddb58822023b8e',\n", " 'd03b6f5b79704351a6e654d7394dc43b',\n", " 'f198cc0415354f158a3cd109fda548db',\n", " '9e2cd909521e4ebea97228cf3d5fdbd0',\n", " '316bbcd0b8b04205a6feede941dd39e7',\n", " '69af230ec01e4b6abf729dbad49124ab',\n", " '2bf68c2994004587b40b4d7867ff0288',\n", " '0198251b019f4b88a90542b4ef956ef7']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.add_documents(documents=docs)"]}, {"cell_type": "code", "execution_count": null, "id": "bdee5d42", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "test_agent", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}