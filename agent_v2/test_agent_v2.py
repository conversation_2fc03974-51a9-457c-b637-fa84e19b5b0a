"""
Test script for Agent V2 - Modern LangChain implementation
Demonstrates the new LangGraph-based memory management
"""

import logging
import sys
import os

# Add parent directory to path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent_v2.main_agent import MainAgentV2

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_agent_v2():
    """Test the new Agent V2 implementation"""
    
    print("🚀 Testing Agent V2 - Modern LangChain Implementation")
    print("=" * 60)
    
    try:
        # Initialize the agent
        agent = MainAgentV2()
        print("✅ Agent V2 initialized successfully")
        
        # Test conversation with memory
        thread_id = "test_conversation_1"
        
        print(f"\n💬 Starting conversation (Thread: {thread_id})")
        print("-" * 40)
        
        # Test 1: Simple greeting
        print("\n👤 User: Hello!")
        response = agent.chat("Hello!", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 2: Introduce name
        print("\n👤 User: My name is <PERSON> and I live in Kathmandu")
        response = agent.chat("My name is <PERSON> and I live in <PERSON>hmandu", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 3: Test memory - ask about name
        print("\n👤 User: What's my name?")
        response = agent.chat("What's my name?", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 4: Course inquiry
        print("\n👤 User: What courses do you have?")
        response = agent.chat("What courses do you have?", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 5: App problem (should use context)
        print("\n👤 User: Mero app chalena")
        response = agent.chat("Mero app chalena", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test 6: Follow-up about the app (should remember context)
        print("\n👤 User: Ambition Guru")
        response = agent.chat("Ambition Guru", thread_id)
        print(f"🤖 Agent: {response}")
        
        # Test conversation history
        print(f"\n📚 Conversation History (Thread: {thread_id}):")
        history = agent.get_conversation_history(thread_id)
        for i, msg in enumerate(history[-6:], 1):  # Show last 6 messages
            role = "👤 User" if hasattr(msg, 'content') and msg.__class__.__name__ == "HumanMessage" else "🤖 Agent"
            content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            print(f"  {i}. {role}: {content}")
        
        # Test new conversation thread
        print(f"\n🆕 Starting new conversation (Thread: test_conversation_2)")
        print("-" * 40)
        
        print("\n👤 User: What's my name?")
        response = agent.chat("What's my name?", "test_conversation_2")
        print(f"🤖 Agent: {response}")
        
        print("\n✅ Agent V2 testing completed successfully!")
        print("🎯 Key improvements demonstrated:")
        print("   • LangGraph-based memory management")
        print("   • Automatic conversation context handling")
        print("   • No hardcoded context manipulation")
        print("   • Thread-based conversation isolation")
        print("   • Modern LangChain 2025 patterns")
        
    except Exception as e:
        logger.error(f"❌ Error testing Agent V2: {e}")
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    test_agent_v2()
